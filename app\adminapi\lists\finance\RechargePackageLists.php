<?php

namespace app\adminapi\lists\finance;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\recharge\RechargePackage;


/**
 * RechargePackage列表
 * Class RechargePackageLists
 * @package app\adminapi\lists
 */
class RechargePackageLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function setSearch(): array
    {
        return [];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function lists(): array
    {
        return RechargePackage::where($this->searchWhere)
            ->field(['id', 'coin_amount', 'bonus_coin', 'price', 'icon', 'create_time', 'update_time'])
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['id' => 'desc'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function count(): int
    {
        return RechargePackage::where($this->searchWhere)->count();
    }

}