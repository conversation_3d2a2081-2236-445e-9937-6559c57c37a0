<?php

namespace app\applent\logic\video;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
use app\common\service\agora\AgoraService;
use app\common\service\LogService;
use app\common\service\ConfigService;
use app\common\model\user\UserRebateRatio;
use app\common\model\gift\GiftRecord;

use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
/**
 * 视频通话业务逻辑类
 */
class VideoCallLogic extends BaseLogic
{
    /**
     * 发起通话
     * @param array $params 通话参数
     * @return array|false
     */
    public static function startCall($params)
    {
        Db::startTrans();
        try {
            if($params['to_user_id'] == $params['user_id']){
                throw new \Exception('不能给自己发起通话');
            }

            // 检查用户状态
            if (VideoCallRecord::isUserInCall($params['user_id'])) {
                throw new \Exception('您正在通话中，无法发起新的通话');
            }

            if (VideoCallRecord::isUserInCall($params['to_user_id'])) {
                throw new \Exception('对方正在通话中，请稍后再试');
            }

            //查找接听人信息
            $toUserInfo = User::where('id', $params['to_user_id'])
                        ->field('id,nickname,avatar,sex,video_price,voice_price,is_service_staff,is_auth,is_disable')
                        ->find();
            if (!$toUserInfo) {
                throw new \Exception('您所拨打的用户不存在');
            }
            if($toUserInfo['is_disable']){
                throw new \Exception('该用户已禁用');
            }
            if(!$toUserInfo['is_service_staff'] && $toUserInfo['sex'] == 2 && $toUserInfo['is_auth'] != 1){
                throw new \Exception('对方未认证，无法发起通话');
            }
            //检查接听人每分钟价格
            $price = $params['call_type'] == 1 ? $toUserInfo['video_price'] : $toUserInfo['voice_price'];
            if($toUserInfo['is_service_staff']){
                $price = 0;
            }

            //查找发起人余额
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $userBalance ? $userBalance['balance'] : 0;
            if ($price > $beforeSenderBalance) {
                throw new \Exception('余额不足，请先充值');
            }
            
            //生成频道ID和Token
            $agoraService = new AgoraService();
            $channelId = $agoraService->generateChannelId($params['user_id'], $params['to_user_id']);      //通道ID
            $callerToken = $agoraService->generateRtcToken($params['user_id'], $channelId, 'publisher');   //发起人Token
            $calleeToken = $agoraService->generateRtcToken($params['to_user_id'], $channelId, 'publisher');//接听人Token

            // 创建通话记录
            $callRecord = [
                'user_id'           => $params['user_id'],
                'call_be_user_id'   => $params['to_user_id'],
                'channel_id'        => $channelId,
                'status'            => 0,
                'type'              => $params['call_type'],
                'duration'          => 0,
                'unit_price'        => $price,
                'total_coin'        => 0,
                'hangup_user_id'    => 0,
                'ip'                => request()->ip(),
                'create_time'       =>time(),
                'end_time'          => 0,
                'turn_time'         => 0,
                'heartbeat_time'    => 0,
            ];
            $callRecordId = VideoCallRecord::insertGetId($callRecord);
            if (!$callRecordId) {
                return self::setError('创建通话记录失败');
            }

            Db::commit();

            return [
                'call_id'           => $callRecordId,
                'channel_id'        => $channelId,
                'user_token'        => $callerToken,
                'to_user_token'     => $calleeToken,
                'video_deduction'   => $price,
                'to_user_info'      => [
                    'id'            => $toUserInfo->id,
                    'nickname'      => $toUserInfo->nickname,
                    'avatar'        => $toUserInfo->avatar,
                    'video_price'   => $toUserInfo->video_price,
                    'voice_price'   => $toUserInfo->voice_price,
                ],
            ];

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 接通通话
     * @param array $params 接通参数
     * @return array|false
     */
    public static function answerCall($params)
    {
        Db::startTrans();
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 检查通话状态（只能接通发起状态的通话）
            if ($callRecord['status'] != VideoCallRecord::STATUS_INITIATED) {
                throw new \Exception('该通话无法接通');
            }

            // 检查用户权限（只有被叫用户才能接通）
            if ($params['user_id'] != $callRecord['call_be_user_id']) {
                throw new \Exception('您没有权限接通此通话');
            }

            $currentTime = time();

            // 更新通话记录状态为已接通
            $updateData = [
                'status'        => VideoCallRecord::STATUS_CONNECTED,
                'turn_time'     => $currentTime,
                'update_time'   => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 修改双方用户状态为忙碌
            User::where('id', $callRecord['user_id'])->update(['is_busy' => 1]);
            User::where('id', $callRecord['call_be_user_id'])->update(['is_busy' => 1]);

            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 实时扣费API（前端每5秒轮询）
     * @param array $params 扣费参数
     * @return array|false
     */
    public static function realTimeCharge($params)
    {
        try {
            $where = [
                'id' => $params['call_id'],
                'status' => 1,
            ];
            // 查找通话记录
            $callRecord = VideoCallRecord::where($where)->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 检查用户权限（只有付费方才能调用扣费接口）
            $payUserId = $callRecord['user_id']; // 发起人付费
            if ($params['user_id'] != $payUserId) {
                throw new \Exception('您没有权限调用此接口');
            }

            // 检查接通时间
            if (!$callRecord['turn_time']) {
                throw new \Exception('通话接通时间异常');
            }

            $currentTime = time();
            // 通话时长（秒）
            $callDuration = $currentTime - $callRecord['turn_time']; 
            //上次心跳时间
            $lastChargeTime = $callRecord['heartbeat_time'];    
             //每分钟扣费金币数量
            $unitPrice = $callRecord['unit_price'];        

            //查看当前余额
            $userBalance = UserBalance::getUserBalance($payUserId);
            $currentBalance = $userBalance ? $userBalance['balance'] : 0;

            // 检查是否需要扣费
            if ((!$lastChargeTime && $callDuration >= 5) || ($lastChargeTime && ($currentTime - $lastChargeTime) >= 60)) {
                // 满足扣费条件：首次扣费（通话满5秒）或后续扣费（距离上次扣费满60秒）
                $chargeMessage = !$lastChargeTime ? '首次扣费成功' : '扣费成功';

                // 计算所需余额：当前扣费 + 下一分钟扣费
                $requiredBalance = $unitPrice * 2;

                if ($currentBalance < $requiredBalance) {
                    // 余额不足，结束通话
                    self::endCallForInsufficientBalance($callRecord);
                    throw new \Exception('余额不足以支付后续通话费用，通话已结束');
                }

                // 开始扣费
                Db::startTrans();

                // 扣除用户余额
                $result = UserBalance::subBalance($callRecord['user_id'], $unitPrice);
                if (!$result) {
                    Db::rollback();
                    throw new \Exception('扣费失败');
                }

                // 计算接听用户实际收益（扣除平台抽成）
                $receiverUser = User::where('id', $callRecord['call_be_user_id'])->find();
                $commissionRate = self::calculateVideoCallCommissionRate($receiverUser);
                $actualIncome = round($unitPrice * (100 - $commissionRate) / 100, 2);

                // 更新通话记录的心跳时间和总消费及通话时长
                VideoCallRecord::where('id', $params['call_id'])
                    ->inc('total_coin', $unitPrice)
                    ->inc('total_income', $actualIncome) // 增加接听用户的实际收益（扣除抽成）
                    ->update([
                        'heartbeat_time' => $currentTime,
                        'update_time' => $currentTime,
                        'duration' => $callDuration,
                        'commission_rate' => $commissionRate, // 记录抽成比例
                    ]);

                Db::commit();

                // 返回发起人和接收人的数据
                return self::buildBothUserData($callRecord, $unitPrice, $currentBalance, $callDuration, $currentTime, $chargeMessage, $actualIncome, true);
            }

            // 不满足扣费条件，返回等待状态
            $message = !$lastChargeTime ? '通话时长不足5秒，无需扣费' : '距离上次扣费不足1分钟，无需扣费';
            return self::buildBothUserData($callRecord, $unitPrice, $currentBalance, $callDuration, $currentTime, $message, 0, false);

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }



    /**
     * 余额不足时结束通话
     * @param array $callRecord 通话记录
     * @return void
     */
    public static function endCallForInsufficientBalance($callRecord)
    {
        $currentTime = time();

        // 开启事务
        Db::startTrans();
        try {
            // 计算实际通话时长（秒）
            $actualDuration = 0;
            if ($callRecord['turn_time'] > 0) {
                $actualDuration = $currentTime - $callRecord['turn_time'];
            }

            // 计算实际应该扣除的金币数量（按分钟计算，不足1分钟按1分钟算）
            // 小于5秒不扣费，5秒及以上按分钟计费
            $actualMinutes = 0;
            if ($actualDuration >= 5) {
                $actualMinutes = ceil($actualDuration / 60);
            }
            $actualCoin = $actualMinutes * $callRecord['unit_price'];

            // 获取接听用户信息，计算实际抽成比例和收益
            $receiverUser = User::where('id', $callRecord['call_be_user_id'])->find();
            $actualCommissionRate = self::calculateVideoCallCommissionRate($receiverUser);
            $actualIncome = round($actualCoin * (100 - $actualCommissionRate) / 100, 2);

            // 处理发起人余额扣除
            if ($actualCoin > 0) {
                // 获取发起人当前余额
                $userBalance = UserBalance::getUserBalance($callRecord['user_id']);
                if (!$userBalance) {
                    $userBalance = UserBalance::createUserBalance($callRecord['user_id']);
                }
                $beforeBalance = $userBalance['balance'];

                // 检查实际应扣费用与已扣费用的差额（使用精确计算避免浮点数精度问题）
                $totalPaidCoin = $callRecord['total_coin'] ?? 0;
                $comparison = bccomp($actualCoin, $totalPaidCoin, 2);

                if ($comparison > 0) {
                    // 实际费用 > 已扣费用，需要补扣差价
                    $balanceDifference = bcsub($actualCoin, $totalPaidCoin, 2);
                    UserBalance::subBalance($callRecord['user_id'], $balanceDifference);
                } elseif ($comparison < 0) {
                    // 实际费用 < 已扣费用，多扣了需要退还
                    $balanceDifference = bcsub($totalPaidCoin, $actualCoin, 2);
                    UserBalance::addBalance($callRecord['user_id'], $balanceDifference);
                }

                // 获取变动后余额（使用精确计算）
                $afterBalance = bcsub($beforeBalance, $actualCoin, 2);

                // 记录发起人余额变动（使用实际花费金币数量）
                UserCoinLog::addVideoCallLog(
                    $callRecord['user_id'],
                    $beforeBalance,
                    $afterBalance,
                    -$actualCoin,
                    'video_call_' . $callRecord['id'],
                    $callRecord['call_be_user_id'],
                    $actualDuration,
                    $callRecord['unit_price']
                );
            }

            // 处理接听人收益增加
            if ($actualIncome > 0) {
                // 获取接听人当前收益
                $receiverBalance = UserBalance::getUserBalance($callRecord['call_be_user_id']);
                if (!$receiverBalance) {
                    $receiverBalance = UserBalance::createUserBalance($callRecord['call_be_user_id']);
                }
                $beforeIncome = $receiverBalance['income'];

                // 增加接听人收益
                UserBalance::addIncome($callRecord['call_be_user_id'], $actualIncome);

                // 获取变动后收益（使用精确计算）
                $afterIncome = bcadd($beforeIncome, $actualIncome, 2);

                // 记录接听人收益变动
                UserIncomeLog::addVideoCallIncomeLog(
                    $callRecord['call_be_user_id'],
                    $beforeIncome,
                    $afterIncome,
                    $actualIncome,
                    'video_call_' . $callRecord['id'],
                    $callRecord['user_id'],
                    $actualCommissionRate,
                    $actualCoin,
                    $actualDuration,
                    $callRecord['unit_price']
                );
            }

            // 更新通话记录状态为结束，并记录实际数据
            VideoCallRecord::where('id', $callRecord['id'])->update([
                'status' => VideoCallRecord::STATUS_ENDED,
                'end_time' => $currentTime,
                'hangup_user_id' => $callRecord['user_id'], // 付费方挂断
                'duration' => $actualDuration, // 实际通话时长
                'actual_coin' => $actualCoin, // 实际应该花费的金币数量
                'actual_income' => $actualIncome, // 实际应该获得的收益
                'actual_commission_rate' => $actualCommissionRate, // 实际抽成比例
                'update_time' => $currentTime
            ]);

            // 恢复双方用户状态
            User::where('id', $callRecord['user_id'])->update(['is_busy' => 0]);
            User::where('id', $callRecord['call_be_user_id'])->update(['is_busy' => 0]);

            // 处理返佣（类似礼物返佣）
            if ($actualCoin > 0) {
                $commissionInfo = calculate_user_commission($callRecord['call_be_user_id'], $actualCoin, 3); // 类型3=通话
                if (!empty($commissionInfo)) {
                    self::handleVideoCallCommissionReward($commissionInfo, 'video_call_' . $callRecord['id'], $callRecord['call_be_user_id'], $actualCoin);
                }
            }

            // 提交事务
            Db::commit();

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 结束通话（已接通时）
     * @param array $params 参数
     * @return array|false
     */
    public static function endCall($params)
    {
        try {
            // 验证参数
            if (empty($params['call_id'])) {
                throw new \Exception('通话ID不能为空');
            }

            // 查询通话记录
            $callRecord = VideoCallRecord::where(['id' => $params['call_id'], 'status' => VideoCallRecord::STATUS_CONNECTED])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 验证用户权限（只有通话参与者可以结算）
            if (!in_array($params['user_id'], [$callRecord['user_id'], $callRecord['call_be_user_id']])) {
                throw new \Exception('您没有权限结算此通话');
            }

            // 调用余额不足结算逻辑
            self::endCallForInsufficientBalance($callRecord->toArray());

            return [
                'call_id' => $callRecord['id'],
                'message' => '通话已结算完成',
                'settle_time' => time()
            ];

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 挂断通话（未接通时）
     * @param array $params 挂断参数
     * @return array|false
     */
    public static function hangupCall($params)
    {
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 只处理未接通的通话（状态为0）
            if ($callRecord['status']) {
                throw new \Exception('该通话已接通或已结束');
            }

            // 检查用户权限（只有通话参与者才能挂断）
            if (!in_array($params['user_id'], [$callRecord['user_id'], $callRecord['call_be_user_id']])) {
                throw new \Exception('您没有权限挂断此通话');
            }

            // 根据挂断类型设置状态
            $status = VideoCallRecord::STATUS_CANCELLED; // 默认取消

            switch ($params['hangup_type']) {
                case 1: // 超时
                    $status = VideoCallRecord::STATUS_TIMEOUT;
                    break;
                case 2: // 发起人挂断
                    if ($params['user_id'] != $callRecord['user_id']) {
                        throw new \Exception('只有发起人才能执行发起人挂断操作');
                    }
                    $status = VideoCallRecord::STATUS_CANCELLED;
                    break;
                case 3: // 接听人挂断（拒绝）
                    if ($params['user_id'] != $callRecord['call_be_user_id']) {
                        throw new \Exception('只有接听人才能执行接听人挂断操作');
                    }
                    $status = VideoCallRecord::STATUS_REJECTED;
                    break;
                case 4: // 系统挂断
                    $status = VideoCallRecord::STATUS_ABNORMAL;
                    break;
                default:
                    throw new \Exception('无效的挂断类型');
            }

            $currentTime = time();

            // 更新通话记录
            $updateData = [
                'status'          => $status,
                'end_time'        => $currentTime,
                'hangup_user_id'  => $params['user_id'],
                'update_time'     => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 删除缓存
            Cache::delete('video_call_' . $callRecord['channel_id']);

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 构建发起人和接收人的数据
     * @param array $callRecord 通话记录
     * @param float $unitPrice 单价
     * @param float $currentBalance 当前余额
     * @param int $callDuration 通话时长
     * @param int $currentTime 当前时间
     * @param string $message 消息
     * @param float $actualIncome 实际收益
     * @param bool $needCharge 是否需要扣费
     * @return array
     */
    private static function buildBothUserData($callRecord, $unitPrice, $currentBalance, $callDuration, $currentTime, $message, $actualIncome = 0, $needCharge = true)
    {
        // 发起用户数据
        $remainingBalance = $needCharge ? $currentBalance - $unitPrice : $currentBalance;
        $nextChargeIn = $needCharge ? 60 : (!$callRecord['heartbeat_time'] ? (5 - $callDuration) : (60 - ($currentTime - $callRecord['heartbeat_time'])));

        $currencyName = ConfigService::get('systemconfig', 'currency_name', '金币');
        $initiatorData = [
            'user_id' => $callRecord['user_id'],
            'total_consumed' => $callRecord['total_coin'] + ($needCharge ? $unitPrice : 0).$currencyName, // 当前消费金币数量
            'per_minute_price' => $unitPrice.$currencyName, // 每分钟单价
            'remaining_balance' => $remainingBalance.$currencyName, // 当前剩余金币数量
            'call_duration' => $callDuration, // 当前通话了多少秒
            'next_charge_in' => max(0, $nextChargeIn), // 距离下次扣费还有多少秒
            'can_afford_next' => $remainingBalance >= $unitPrice, // 是否能支付下一分钟
            'balance_warning' => $remainingBalance >= ($unitPrice * 2) // 余额是否支持超过两分钟警告
        ];

        $profitName = ConfigService::get('systemconfig', 'profit_name', '钻石');
        // 接听用户数据
        $currentVideoIncome = $callRecord['total_income'] + $actualIncome; // 当前视频收益

        // 查询当前通话的礼物收益
        $giftIncome = GiftRecord::where([
            'user_id' => $callRecord['user_id'], // 发起用户
            'to_user_id' => $callRecord['call_be_user_id'], // 接听用户
            'source_type' => GiftRecord::SOURCE_VIDEO_CALL, // 视频通话来源
            'source_id' => $callRecord['id'] // 当前通话记录ID
        ])->sum('actual_income') ?: 0;

        $receiverData = [
            'user_id' => $callRecord['call_be_user_id'],
            'current_video_income' => number_format($currentVideoIncome, 2).$profitName, // 当前的视频收益
            'current_gift_income' => number_format($giftIncome, 2).$profitName, // 当前视频的礼物收益
            'per_minute_price' => $unitPrice.$currencyName, // 每分钟单价
            'call_duration' => $callDuration, // 当前通话了多少秒
            'total_income' => number_format($currentVideoIncome + $giftIncome, 2).$profitName // 总收益
        ];

        return [
            'need_charge' => $needCharge,
            'message' => $message,
            'initiator_data' => $initiatorData, // 发起人数据
            'receiver_data' => $receiverData    // 接收人数据
        ];
    }

    /**
     * 计算视频通话抽成比例
     * @param array $receiverUser 接听用户信息
     * @return float 抽成比例
     */
    private static function calculateVideoCallCommissionRate($receiverUser)
    {
        // 检查用户是否开启个人抽成设置
        if ($receiverUser['is_open_audio_commission'] == 1) {
            // 从用户返佣比例表获取
            $userRebate = UserRebateRatio::where('user_id', $receiverUser['id'])->find();
            if ($userRebate && isset($userRebate->audio_commission)) {
                return floatval($userRebate->audio_commission);
            }
        }

        // 使用系统默认配置
        return floatval(ConfigService::get('systemconfig', 'audio_commission', 50));
    }

    /**
     * @notes 处理视频通话返佣奖励
     * @param array $commissionInfo 返佣信息
     * @param string $orderNo 订单号
     * @param int $receiverId 接听人ID
     * @param float $totalAmount 通话总金额
     * @return bool
     */
    private static function handleVideoCallCommissionReward($commissionInfo, $orderNo, $receiverId, $totalAmount)
    {
        try {
            foreach ($commissionInfo as $level => $info) {
                if (empty($info) || $info['commission_amount'] <= 0) {
                    continue;
                }

                // 获取返佣用户当前收益
                $userBalance = UserBalance::getUserBalance($info['user_id']);
                if (!$userBalance) {
                    $userBalance = UserBalance::createUserBalance($info['user_id']);
                }
                $beforeIncome = $userBalance['income'];

                // 增加返佣收益
                $addResult = UserBalance::addIncome($info['user_id'], $info['commission_amount']);
                if (!$addResult) {
                    continue;
                }

                // 获取变动后收益
                $afterIncome = bcadd($beforeIncome, $info['commission_amount'], 2);

                // 记录返佣收益日志
                UserIncomeLog::addVideoCallCommissionLog(
                    $info['user_id'],
                    $beforeIncome,
                    $afterIncome,
                    $info['commission_amount'],
                    $level,
                    $orderNo,
                    $receiverId, // 来源用户是接听人
                    $info['commission_rate'],
                    $totalAmount
                );

            }

            return true;
        } catch (\Exception $e) {
            // 返佣失败不影响主流程，只记录日志
            Log::error('视频通话返佣处理失败：' . $e->getMessage());
            return false;
        }
    }
}
