# 余额不足结算功能事务优化

## 概述

对 `endCallForInsufficientBalance` 方法进行了重大优化，增加了数据库事务处理、余额变动记录和收益处理功能，确保通话结算的数据一致性和完整性。

## 主要改进

### 1. 事务处理
- 使用 `Db::startTrans()` 开启事务
- 所有数据库操作包装在 try-catch 块中
- 异常时自动回滚，成功时提交事务
- 确保数据一致性，避免部分更新导致的数据不一致

### 2. 发起人余额处理
- **差价补扣机制**：比较实际应扣费用与已扣费用
  - 如果实际费用 > 已扣费用：补扣差价
  - 如果实际费用 < 已扣费用：退还多扣金额（保护性处理）
- **余额变动记录**：使用 `UserCoinLog::addLog` 记录详细的余额变动
  - 记录变动前后余额
  - 记录实际消费金币数量
  - 包含通话详细信息（时长、单价、分钟数）

### 3. 接听人收益处理
- **收益增加**：根据实际通话时长和抽成比例计算收益
- **收益变动记录**：使用 `UserIncomeLog::addLog` 记录收益变动
  - 记录变动前后收益
  - 记录抽成比例和总收入
  - 包含来源用户信息

### 4. 数据完整性保障
- 自动创建用户余额记录（如果不存在）
- 准确计算变动前后的余额和收益
- 详细的日志记录便于追踪和审计

## 技术实现

### 核心逻辑流程
```
1. 开启数据库事务
2. 计算实际通话时长和费用
3. 处理发起人余额扣除
   - 获取当前余额
   - 计算差价并处理
   - 记录余额变动日志
4. 处理接听人收益增加
   - 获取当前收益
   - 增加实际收益
   - 记录收益变动日志
5. 更新通话记录状态
6. 恢复用户状态
7. 提交事务
```

### 使用的模型和方法
- `UserBalance::getUserBalance()` - 获取用户余额信息
- `UserBalance::createUserBalance()` - 创建用户余额记录
- `UserBalance::subBalance()` - 扣除用户余额
- `UserBalance::addBalance()` - 增加用户余额
- `UserBalance::addIncome()` - 增加用户收益
- `UserCoinLog::addLog()` - 记录金币变动日志
- `UserIncomeLog::addLog()` - 记录收益变动日志

### 日志记录详情

#### 余额变动日志
- 用户ID、变动前后余额、变动金额
- 来源：语音视频通话 (`UserCoinLog::SOURCE_CALL`)
- 类型：减少 (`UserCoinLog::TYPE_SUB`)
- 订单号：`video_call_{通话记录ID}`
- 目标用户：接听人ID

#### 收益变动日志
- 用户ID、变动前后收益、变动金额
- 来源：语音视频收益 (`UserIncomeLog::SOURCE_CALL_INCOME`)
- 类型：增加 (`UserIncomeLog::TYPE_ADD`)
- 订单号：`video_call_{通话记录ID}`
- 来源用户：发起人ID
- 抽成比例和总收入信息

## 错误处理

### 异常情况处理
1. **数据库操作失败**：自动回滚事务，抛出异常
2. **余额不足**：`UserBalance::subBalance` 会检查余额是否充足
3. **用户不存在**：自动创建用户余额记录
4. **计算错误**：使用保护性处理，避免数据异常

### 事务回滚场景
- 余额扣除失败
- 收益增加失败
- 日志记录失败
- 通话记录更新失败
- 用户状态更新失败

## 数据一致性保证

1. **原子性**：所有操作在一个事务中完成
2. **一致性**：余额变动与日志记录保持一致
3. **隔离性**：事务期间数据不受其他操作影响
4. **持久性**：提交后数据永久保存

## 使用示例

```php
// 调用示例
$callRecord = VideoCallRecord::where('id', $callId)->find();
VideoCallLogic::endCallForInsufficientBalance($callRecord);
```

## 注意事项

1. **性能考虑**：事务会锁定相关数据，应尽快完成操作
2. **并发处理**：多个并发请求可能导致死锁，需要合理设计
3. **日志存储**：大量通话会产生大量日志，需要定期清理
4. **监控告警**：建议对事务失败进行监控和告警

## 测试建议

1. **正常流程测试**：验证完整的结算流程
2. **异常情况测试**：模拟各种异常情况
3. **并发测试**：测试高并发场景下的数据一致性
4. **数据验证**：检查余额、收益和日志的准确性

## 后续优化建议

1. **性能优化**：考虑使用队列异步处理日志记录
2. **监控完善**：添加详细的业务监控指标
3. **数据分析**：基于日志数据进行业务分析
4. **自动对账**：定期对账确保数据准确性
