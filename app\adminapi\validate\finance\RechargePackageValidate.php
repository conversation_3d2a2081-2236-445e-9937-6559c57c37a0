<?php

namespace app\adminapi\validate\finance;


use app\common\validate\BaseValidate;


/**
 * RechargePackage验证器
 * Class RechargePackageValidate
 * @package app\adminapi\validate
 */
class RechargePackageValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require',

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',

    ];


    /**
     * @notes 添加场景
     * @return RechargePackageValidate
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function sceneAdd()
    {
        return $this->remove('id', true);
    }


    /**
     * @notes 编辑场景
     * @return RechargePackageValidate
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function sceneEdit()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 删除场景
     * @return RechargePackageValidate
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return RechargePackageValidate
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}