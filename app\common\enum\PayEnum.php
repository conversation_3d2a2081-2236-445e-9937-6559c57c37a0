<?php


namespace app\common\enum;


/**
 * 支付
 * Class PayrEnum
 * @package app\common\enum
 */
class PayEnum
{

    const YES = 1;
    const NO = 0;

    //支付类型
    const WECHAT_PAY   = 1; //微信APP支付
    const ALI_PAY    = 2; //微信H5支付
    const WECHAT_H5       = 3; //支付宝APP支付
    const ALI_H5       = 4; //支付宝H5支付


    //支付状态
    const WTPAID = 0; //待支付
    const CEPAID = 1; //取消支付
    const ISPAID = 2; //支付成功
    const UNPAID = 3; //支付失败
    const IGPAID = 4; //支付中



    //支付场景
    const SCENE_H5 = 1;//H5
    const SCENE_OA = 2;//微信公众号
    const SCENE_MNP = 3;//微信小程序
    const SCENE_APP = 4;//APP
    const SCENE_PC = 5;//PC商城


    /**
     * @notes 获取支付类型
     * @param bool $value
     * @return string|string[]
     */
    public static function getPayTypeDesc($value = true)
    {
        $data = [
            self::WECHAT_PAY => '微信支付',
            self::WECHAT_H5 => '微信H5支付',
            self::ALI_PAY => '支付宝支付',
            self::ALI_H5 => '支付宝H5支付',
        ];
        if ($value === true) {
            return $data;
        }
        return $data[$value] ?? '';
    }



    /**
     * @notes 支付状态
     * @param bool $value
     * @return string|string[]
     */
    public static function getPayStatusDesc($value = true)
    {
        $data = [
            self::WTPAID => '待支付',
            self::CEPAID => '取消支付',
            self::ISPAID => '支付成功',
            self::UNPAID => '支付失败',
            self::IGPAID => '支付中',
        ];
        if ($value === true) {
            return $data;
        }
        return $data[$value] ?? '';
    }

    public static function getIsFirstOrderDesc($value = true)
    {
        $data = [
            self::YES => '是',
            self::NO => '否'
        ];
        if ($value === true) {
            return $data;
        }
        return $data[$value];
    }



    /**
     * @notes 支付场景
     * @param bool $value
     * @return string|string[]
     */
    public static function getPaySceneDesc($value = true)
    {
        $data = [
            self::SCENE_H5 => 'H5',
            self::SCENE_OA => '微信公众号',
            self::SCENE_MNP => '微信小程序',
            self::SCENE_APP => 'APP',
            self::SCENE_PC => 'PC',
        ];
        if ($value === true) {
            return $data;
        }
        return $data[$value] ?? '';
    }


}