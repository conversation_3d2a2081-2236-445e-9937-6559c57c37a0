# 余额不足结算功能测试脚本

## 测试环境准备

### 1. 数据库准备
确保已执行数据库迁移脚本：
```sql
-- 添加实际结算字段
ALTER TABLE `la_video_call_record` 
ADD COLUMN `actual_coin` decimal(10,2) DEFAULT 0.00 COMMENT '实际花费金币',
ADD COLUMN `actual_income` decimal(10,2) DEFAULT 0.00 COMMENT '实际获得收益', 
ADD COLUMN `actual_commission_rate` decimal(5,2) DEFAULT 0.00 COMMENT '实际抽成比例';
```

### 2. 测试数据准备
```sql
-- 创建测试用户
INSERT INTO `la_user` (`id`, `nickname`, `mobile`, `balance`, `income`) VALUES 
(1001, '测试发起人', '13800001001', 1000, 0),
(1002, '测试接听人', '13800001002', 0, 0);

-- 创建用户余额记录
INSERT INTO `la_user_balance` (`user_id`, `balance`, `income`) VALUES 
(1001, 1000, 0),
(1002, 0, 0);

-- 创建测试通话记录
INSERT INTO `la_video_call_record` (`id`, `user_id`, `call_be_user_id`, `unit_price`, `turn_time`, `total_coin`, `status`) VALUES 
(9001, 1001, 1002, 10, UNIX_TIMESTAMP() - 150, 20, 1);
```

## PHP 测试脚本

### 基础功能测试
```php
<?php
// test_settlement.php

require_once 'vendor/autoload.php';

use app\applent\logic\video\VideoCallLogic;
use app\common\model\VideoCallRecord;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;

class SettlementTest 
{
    /**
     * 测试余额不足结算功能
     */
    public function testEndCallForInsufficientBalance()
    {
        echo "=== 开始测试余额不足结算功能 ===\n";
        
        // 1. 准备测试数据
        $callId = 9001;
        $callRecord = VideoCallRecord::where('id', $callId)->find();
        
        if (!$callRecord) {
            echo "错误：找不到测试通话记录\n";
            return false;
        }
        
        echo "通话记录ID: {$callRecord['id']}\n";
        echo "发起人ID: {$callRecord['user_id']}\n";
        echo "接听人ID: {$callRecord['call_be_user_id']}\n";
        echo "单价: {$callRecord['unit_price']} 金币/分钟\n";
        echo "已扣费用: {$callRecord['total_coin']} 金币\n";
        
        // 2. 获取结算前的余额和收益
        $senderBalanceBefore = UserBalance::getUserBalance($callRecord['user_id']);
        $receiverBalanceBefore = UserBalance::getUserBalance($callRecord['call_be_user_id']);
        
        echo "\n=== 结算前状态 ===\n";
        echo "发起人余额: {$senderBalanceBefore['balance']} 金币\n";
        echo "接听人收益: {$receiverBalanceBefore['income']} 元\n";
        
        // 3. 执行结算
        try {
            VideoCallLogic::endCallForInsufficientBalance($callRecord->toArray());
            echo "\n✅ 结算执行成功\n";
        } catch (\Exception $e) {
            echo "\n❌ 结算执行失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        // 4. 检查结算后的状态
        $callRecordAfter = VideoCallRecord::where('id', $callId)->find();
        $senderBalanceAfter = UserBalance::getUserBalance($callRecord['user_id']);
        $receiverBalanceAfter = UserBalance::getUserBalance($callRecord['call_be_user_id']);
        
        echo "\n=== 结算后状态 ===\n";
        echo "通话状态: {$callRecordAfter['status']} (2=已结束)\n";
        echo "实际时长: {$callRecordAfter['duration']} 秒\n";
        echo "实际花费: {$callRecordAfter['actual_coin']} 金币\n";
        echo "实际收益: {$callRecordAfter['actual_income']} 元\n";
        echo "抽成比例: {$callRecordAfter['actual_commission_rate']}%\n";
        echo "发起人余额: {$senderBalanceAfter['balance']} 金币\n";
        echo "接听人收益: {$receiverBalanceAfter['income']} 元\n";
        
        // 5. 检查日志记录
        $coinLog = UserCoinLog::where('source_no', 'video_call_' . $callId)->find();
        $incomeLog = UserIncomeLog::where('source_no', 'video_call_' . $callId)->find();
        
        echo "\n=== 日志记录检查 ===\n";
        if ($coinLog) {
            echo "✅ 金币变动日志已记录\n";
            echo "   变动金额: {$coinLog['change_coin']} 金币\n";
            echo "   变动前: {$coinLog['before_coin']} 金币\n";
            echo "   变动后: {$coinLog['after_coin']} 金币\n";
        } else {
            echo "❌ 金币变动日志未找到\n";
        }
        
        if ($incomeLog) {
            echo "✅ 收益变动日志已记录\n";
            echo "   变动金额: {$incomeLog['change_income']} 元\n";
            echo "   变动前: {$incomeLog['before_income']} 元\n";
            echo "   变动后: {$incomeLog['after_income']} 元\n";
        } else {
            echo "❌ 收益变动日志未找到\n";
        }
        
        return true;
    }
    
    /**
     * 测试数据一致性
     */
    public function testDataConsistency()
    {
        echo "\n=== 数据一致性检查 ===\n";
        
        $callId = 9001;
        $callRecord = VideoCallRecord::where('id', $callId)->find();
        
        // 检查实际花费计算是否正确
        $actualMinutes = ceil($callRecord['duration'] / 60);
        $expectedCoin = $actualMinutes * $callRecord['unit_price'];
        
        if ($callRecord['actual_coin'] == $expectedCoin) {
            echo "✅ 实际花费计算正确: {$callRecord['actual_coin']} 金币\n";
        } else {
            echo "❌ 实际花费计算错误: 期望 {$expectedCoin}，实际 {$callRecord['actual_coin']}\n";
        }
        
        // 检查收益计算是否正确
        $expectedIncome = round($callRecord['actual_coin'] * (100 - $callRecord['actual_commission_rate']) / 100, 2);
        
        if (abs($callRecord['actual_income'] - $expectedIncome) < 0.01) {
            echo "✅ 实际收益计算正确: {$callRecord['actual_income']} 元\n";
        } else {
            echo "❌ 实际收益计算错误: 期望 {$expectedIncome}，实际 {$callRecord['actual_income']}\n";
        }
        
        // 检查余额变动是否一致
        $coinLog = UserCoinLog::where('source_no', 'video_call_' . $callId)->find();
        if ($coinLog && abs($coinLog['change_coin']) == $callRecord['actual_coin']) {
            echo "✅ 余额变动记录一致\n";
        } else {
            echo "❌ 余额变动记录不一致\n";
        }
        
        // 检查收益变动是否一致
        $incomeLog = UserIncomeLog::where('source_no', 'video_call_' . $callId)->find();
        if ($incomeLog && $incomeLog['change_income'] == $callRecord['actual_income']) {
            echo "✅ 收益变动记录一致\n";
        } else {
            echo "❌ 收益变动记录不一致\n";
        }
    }
}

// 执行测试
$test = new SettlementTest();
$test->testEndCallForInsufficientBalance();
$test->testDataConsistency();

echo "\n=== 测试完成 ===\n";
```

## API 测试脚本

### cURL 测试
```bash
#!/bin/bash

# 测试结算通话API
echo "=== 测试结算通话API ==="

# API地址
API_URL="http://your-domain.com/applent/video/end_call"

# 测试数据
curl -X POST $API_URL \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "call_id": 9001
  }' | jq '.'

echo "API测试完成"
```

### Postman 测试集合
```json
{
  "info": {
    "name": "视频通话结算测试",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "结算通话",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"call_id\": 9001\n}"
        },
        "url": {
          "raw": "{{base_url}}/applent/video/end_call",
          "host": ["{{base_url}}"],
          "path": ["applent", "video", "end_call"]
        }
      }
    }
  ]
}
```

## 性能测试

### 并发测试脚本
```php
<?php
// concurrent_test.php

use app\applent\logic\video\VideoCallLogic;
use app\common\model\VideoCallRecord;

class ConcurrentTest 
{
    public function testConcurrentSettlement()
    {
        $processes = [];
        $callIds = [9001, 9002, 9003, 9004, 9005]; // 多个测试通话记录
        
        foreach ($callIds as $callId) {
            $pid = pcntl_fork();
            
            if ($pid == 0) {
                // 子进程
                $this->settleSingleCall($callId);
                exit(0);
            } else {
                $processes[] = $pid;
            }
        }
        
        // 等待所有子进程完成
        foreach ($processes as $pid) {
            pcntl_waitpid($pid, $status);
        }
        
        echo "并发测试完成\n";
    }
    
    private function settleSingleCall($callId)
    {
        try {
            $callRecord = VideoCallRecord::where('id', $callId)->find();
            VideoCallLogic::endCallForInsufficientBalance($callRecord->toArray());
            echo "通话 {$callId} 结算成功\n";
        } catch (\Exception $e) {
            echo "通话 {$callId} 结算失败: " . $e->getMessage() . "\n";
        }
    }
}

$test = new ConcurrentTest();
$test->testConcurrentSettlement();
```

## 测试检查清单

### 功能测试
- [ ] 正常结算流程
- [ ] 余额不足处理
- [ ] 收益计算准确性
- [ ] 抽成比例计算
- [ ] 通话记录状态更新
- [ ] 用户状态恢复

### 数据一致性测试
- [ ] 事务回滚测试
- [ ] 并发访问测试
- [ ] 数据库约束测试
- [ ] 日志记录完整性

### 异常处理测试
- [ ] 数据库连接异常
- [ ] 用户不存在异常
- [ ] 通话记录不存在异常
- [ ] 计算溢出异常

### 性能测试
- [ ] 单次结算耗时
- [ ] 并发结算性能
- [ ] 内存使用情况
- [ ] 数据库锁等待时间

## 测试结果验证

### 数据验证SQL
```sql
-- 检查通话记录更新
SELECT id, status, duration, actual_coin, actual_income, actual_commission_rate 
FROM la_video_call_record 
WHERE id = 9001;

-- 检查用户余额变动
SELECT user_id, balance, income 
FROM la_user_balance 
WHERE user_id IN (1001, 1002);

-- 检查金币变动日志
SELECT * FROM la_user_balance_log 
WHERE source_no = 'video_call_9001';

-- 检查收益变动日志
SELECT * FROM la_user_income_log 
WHERE source_no = 'video_call_9001';
```

## 注意事项

1. **测试环境隔离**：使用独立的测试数据库
2. **数据清理**：每次测试前清理旧数据
3. **权限检查**：确保测试用户有足够权限
4. **日志监控**：关注测试过程中的错误日志
5. **性能监控**：记录测试过程中的性能指标
