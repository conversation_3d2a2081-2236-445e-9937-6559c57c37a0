<?php

namespace app\adminapi\logic\finance;


use app\common\logic\BaseLogic;
use app\common\model\recharge\RechargePackage;
use think\facade\Db;


/**
 * RechargePackage逻辑
 * Class RechargePackageLogic
 * @package app\adminapi\logic
 */
class RechargePackageLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            RechargePackage::create([
                'coin_amount' => $params['coin_amount'],
                'bonus_coin' => $params['bonus_coin'],
                'price' => $params['price'],
                'icon' => $params['icon'],
                'sort' => $params['sort'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            RechargePackage::where('id', $params['id'])->update([
                'coin_amount' => $params['coin_amount'],
                'bonus_coin' => $params['bonus_coin'],
                'price' => $params['price'],
                'icon' => $params['icon'],
                'sort' => $params['sort'],
                'update_time' => time(),
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public static function delete(array $params): bool
    {
        return RechargePackage::destroy($params['id']);
    }


    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public static function detail($params): array
    {
        return RechargePackage::findOrEmpty($params['id'])->toArray();
    }
}