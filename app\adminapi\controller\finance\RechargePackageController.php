<?php


namespace app\adminapi\controller\finance;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\finance\RechargePackageLists;
use app\adminapi\logic\finance\RechargePackageLogic;
use app\adminapi\validate\finance\RechargePackageValidate;


/**
 * RechargePackage控制器
 * Class RechargePackageController
 * @package app\adminapi\controller
 */
class RechargePackageController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function lists()
    {
        return $this->dataLists(new RechargePackageLists());
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function add()
    {
        $params = (new RechargePackageValidate())->post()->goCheck('add');
        $result = RechargePackageLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(RechargePackageLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function edit()
    {
        $params = (new RechargePackageValidate())->post()->goCheck('edit');
        $result = RechargePackageLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(RechargePackageLogic::getError());
    }


    /**
     * @notes 删除
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function delete()
    {
        $params = (new RechargePackageValidate())->post()->goCheck('delete');
        RechargePackageLogic::delete($params);
        return $this->success('删除成功', [], 1, 1);
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 09:55
     */
    public function detail()
    {
        $params = (new RechargePackageValidate())->goCheck('detail');
        $result = RechargePackageLogic::detail($params);
        return $this->data($result);
    }


}