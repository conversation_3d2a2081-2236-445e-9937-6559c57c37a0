# 视频通话日志优化和返佣功能

## 概述

参考GiftLogic.php中sendGift方法的设计模式，对VideoCallLogic中的余额变动和收益变动处理进行了优化，并新增了上级返佣功能。

## 主要改进

### 1. 日志记录方法优化

#### 在UserCoinLog中新增专用方法
```php
/**
 * @notes 记录视频通话金币日志
 * @param int $userId 用户ID
 * @param int $beforeCoin 变动前金币数量
 * @param int $afterCoin 变动后金币数量
 * @param int $changeCoin 变动金币数量（负数，表示扣除）
 * @param string $orderNo 通话订单号
 * @param int $toUserId 接听用户ID
 * @param int $duration 通话时长（秒）
 * @param int $unitPrice 单价（金币/分钟）
 * @return bool
 */
public static function addVideoCallLog($userId, $beforeCoin, $afterCoin, $changeCoin, $orderNo, $toUserId, $duration = 0, $unitPrice = 0)
```

#### 在UserIncomeLog中新增专用方法
```php
/**
 * @notes 记录视频通话收益日志
 */
public static function addVideoCallIncomeLog($userId, $beforeIncome, $afterIncome, $incomeAmount, $orderNo, $fromUserId, $commissionRate = 0, $totalAmount = 0, $duration = 0, $unitPrice = 0)

/**
 * @notes 记录视频通话返佣收益日志
 */
public static function addVideoCallCommissionLog($userId, $beforeIncome, $afterIncome, $commissionAmount, $level, $orderNo, $fromUserId, $commissionRate = 0, $totalAmount = 0)
```

### 2. VideoCallLogic中的调用优化

#### 优化前（通用方法）
```php
// 记录发起人余额变动
UserCoinLog::addLog(
    $callRecord['user_id'],
    $beforeBalance,
    $afterBalance,
    -$actualCoin,
    UserCoinLog::SOURCE_CALL,
    UserCoinLog::TYPE_SUB,
    'video_call_' . $callRecord['id'],
    $callRecord['call_be_user_id'],
    ''
);

// 记录接听人收益变动
UserIncomeLog::addLog(
    $callRecord['call_be_user_id'],
    $beforeIncome,
    $afterIncome,
    $actualIncome,
    UserIncomeLog::SOURCE_CALL_INCOME,
    UserIncomeLog::TYPE_ADD,
    'video_call_' . $callRecord['id'],
    $callRecord['user_id'],
    '',
    $actualCommissionRate,
    $actualCoin
);
```

#### 优化后（专用方法）
```php
// 记录发起人余额变动
UserCoinLog::addVideoCallLog(
    $callRecord['user_id'],
    $beforeBalance,
    $afterBalance,
    -$actualCoin,
    'video_call_' . $callRecord['id'],
    $callRecord['call_be_user_id'],
    $actualDuration,
    $callRecord['unit_price']
);

// 记录接听人收益变动
UserIncomeLog::addVideoCallIncomeLog(
    $callRecord['call_be_user_id'],
    $beforeIncome,
    $afterIncome,
    $actualIncome,
    'video_call_' . $callRecord['id'],
    $callRecord['user_id'],
    $actualCommissionRate,
    $actualCoin,
    $actualDuration,
    $callRecord['unit_price']
);
```

### 3. 新增返佣功能

#### 返佣触发逻辑
```php
// 处理返佣（类似礼物返佣）
if ($actualCoin > 0) {
    $commissionInfo = calculate_user_commission($callRecord['call_be_user_id'], $actualCoin, 3); // 类型3=通话
    if (!empty($commissionInfo)) {
        self::handleVideoCallCommissionReward($commissionInfo, 'video_call_' . $callRecord['id'], $callRecord['call_be_user_id'], $actualCoin);
    }
}
```

#### 返佣处理方法
```php
/**
 * @notes 处理视频通话返佣奖励
 * @param array $commissionInfo 返佣信息
 * @param string $orderNo 订单号
 * @param int $receiverId 接听人ID
 * @param float $totalAmount 通话总金额
 * @return bool
 */
private static function handleVideoCallCommissionReward($commissionInfo, $orderNo, $receiverId, $totalAmount)
{
    try {
        foreach ($commissionInfo as $level => $info) {
            if (empty($info) || $info['commission_amount'] <= 0) {
                continue;
            }

            // 获取返佣用户当前收益
            $userBalance = UserBalance::getUserBalance($info['user_id']);
            if (!$userBalance) {
                $userBalance = UserBalance::createUserBalance($info['user_id']);
            }
            $beforeIncome = $userBalance['income'];

            // 增加返佣收益
            $addResult = UserBalance::addIncome($info['user_id'], $info['commission_amount']);
            if (!$addResult) {
                Log::error("视频通话返佣失败：用户ID {$info['user_id']}，金额 {$info['commission_amount']}");
                continue;
            }

            // 获取变动后收益
            $afterIncome = bcadd($beforeIncome, $info['commission_amount'], 2);

            // 记录返佣收益日志
            UserIncomeLog::addVideoCallCommissionLog(
                $info['user_id'],
                $beforeIncome,
                $afterIncome,
                $info['commission_amount'],
                $level,
                $orderNo,
                $receiverId, // 来源用户是接听人
                $info['commission_rate'],
                $totalAmount
            );

            Log::info("视频通话返佣成功：用户ID {$info['user_id']}，级别 {$level}，金额 {$info['commission_amount']}");
        }

        return true;
    } catch (\Exception $e) {
        // 返佣失败不影响主流程，只记录日志
        Log::error('视频通话返佣处理失败：' . $e->getMessage());
        return false;
    }
}
```

## 返佣机制说明

### 返佣计算
- **基础金额**：实际通话消费金额（$actualCoin）
- **返佣对象**：接听人的上级用户（一级、二级）
- **返佣类型**：类型3=通话（区别于礼物、充值）
- **返佣比例**：通过 `calculate_user_commission` 函数获取

### 返佣流程
1. **触发条件**：实际消费金额 > 0
2. **获取返佣信息**：调用 `calculate_user_commission($receiverId, $actualCoin, 3)`
3. **处理返佣**：
   - 增加上级用户收益
   - 记录返佣日志
   - 使用精确计算避免精度问题
4. **异常处理**：返佣失败不影响主流程，只记录错误日志

### 日志记录
- **一级返佣**：SOURCE_CALL_LEVEL1 (7)
- **二级返佣**：SOURCE_CALL_LEVEL2 (8)
- **详细信息**：包含返佣级别、比例、原始金额等

## 优势对比

### 代码可维护性
- ✅ **专用方法**：语义更清晰，参数更简洁
- ✅ **统一模式**：与礼物系统保持一致的设计模式
- ✅ **参数封装**：自动处理常用参数，减少重复代码

### 功能完整性
- ✅ **返佣功能**：新增上级返佣，增加用户粘性
- ✅ **日志完整**：详细记录通话相关信息
- ✅ **异常处理**：返佣失败不影响主流程

### 数据准确性
- ✅ **精确计算**：使用BC Math函数避免精度问题
- ✅ **事务保护**：返佣在事务内执行，保证数据一致性
- ✅ **详细日志**：便于问题排查和数据审计

## API数据结构统一

### 统一返回格式
`realTimeCharge` 和 `endCall` 方法现在返回相同的数据结构：

```php
return [
    'need_charge' => false,  // 是否需要扣费
    'message' => '通话已结算完成',
    'initiator_data' => [    // 发起人数据
        'user_id' => 123,
        'total_consumed' => '30金币',
        'per_minute_price' => '10金币',
        'remaining_balance' => '70金币',
        'call_duration' => 180,
        'next_charge_in' => 0,
        'can_afford_next' => true,
        'balance_warning' => true
    ],
    'receiver_data' => [     // 接听人数据
        'user_id' => 456,
        'current_video_income' => '15.00钻石',
        'current_gift_income' => '5.00钻石',
        'per_minute_price' => '10金币',
        'call_duration' => 180,
        'total_income' => '20.00钻石'
    ]
];
```

## 使用示例

### 实时扣费
```php
// 每5秒调用一次
POST /applent/video/real_time_charge
{
    "call_id": 12345
}
```

### 手动结算
```php
// 通话结束时调用，返回实际结算后的数据
POST /applent/video/end_call
{
    "call_id": 12345
}

// 返回实际结算数据，包含：
// - 实际通话时长
// - 实际消费金币
// - 实际获得收益
// - 结算后的余额
```

### 返佣查询
```sql
-- 查询用户返佣记录
SELECT * FROM la_user_income_log 
WHERE user_id = ? 
AND source IN (7, 8)  -- 通话返佣
ORDER BY create_time DESC;

-- 查询通话相关的所有收益记录
SELECT * FROM la_user_income_log 
WHERE source_no LIKE 'video_call_%'
ORDER BY create_time DESC;
```

## 配置说明

### 返佣比例配置
返佣比例通过 `calculate_user_commission` 函数获取，支持：
- 系统默认配置
- 用户个性化配置
- 分级返佣设置

### 日志存储
- **金币日志**：la_user_balance_log 表
- **收益日志**：la_user_income_log 表
- **通话记录**：la_video_call_record 表

## 注意事项

1. **返佣时机**：在事务内处理，确保数据一致性
2. **异常处理**：返佣失败不影响通话结算主流程
3. **日志监控**：关注返佣相关的错误日志
4. **性能考虑**：返佣计算会增加少量处理时间
5. **数据审计**：定期检查返佣数据的准确性

## 后续优化建议

1. **批量处理**：高并发场景下考虑异步处理返佣
2. **缓存优化**：缓存用户返佣配置信息
3. **监控告警**：添加返佣异常的实时监控
4. **数据统计**：提供返佣数据的统计分析功能
