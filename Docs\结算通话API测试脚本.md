# 结算通话API测试脚本

## 🧪 测试环境准备

### 1. 创建测试数据
```sql
-- 创建测试通话记录
INSERT INTO `la_video_call_record` (
    `user_id`, 
    `call_be_user_id`, 
    `channel_id`, 
    `status`, 
    `unit_price`, 
    `turn_time`, 
    `create_time`, 
    `update_time`
) VALUES (
    1001,                    -- 发起用户ID
    1002,                    -- 接听用户ID  
    'test_channel_123',      -- 频道ID
    1,                       -- 状态：连接中
    10.00,                   -- 单价：10金币/分钟
    UNIX_TIMESTAMP() - 180,  -- 接通时间：3分钟前
    UNIX_TIMESTAMP() - 200,  -- 创建时间：200秒前
    UNIX_TIMESTAMP()         -- 更新时间：当前时间
);

-- 设置用户忙碌状态
UPDATE `la_user` SET `is_busy` = 1 WHERE `id` IN (1001, 1002);
```

### 2. 获取测试用户Token
```bash
# 登录获取token
curl -X POST "http://your-domain/api/login" \
-H "Content-Type: application/json" \
-d '{
    "mobile": "13800001001",
    "password": "123456"
}'
```

## 📋 测试用例

### 测试用例1：正常结算通话
```bash
#!/bin/bash

# 设置变量
API_URL="http://your-domain/api/video/settle_call"
TOKEN="your_user_token_here"
CALL_ID=1

# 发送请求
echo "=== 测试用例1：正常结算通话 ==="
curl -X POST "$API_URL" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $TOKEN" \
-d "{
    \"call_id\": $CALL_ID
}" | jq '.'

# 验证数据库结果
echo -e "\n=== 验证数据库结果 ==="
mysql -u root -p -e "
SELECT 
    id,
    status,
    duration,
    actual_coin,
    actual_income,
    actual_commission_rate,
    hangup_user_id,
    end_time
FROM la_video_call_record 
WHERE id = $CALL_ID;
" your_database_name

echo -e "\n=== 验证用户状态 ==="
mysql -u root -p -e "
SELECT id, is_busy 
FROM la_user 
WHERE id IN (1001, 1002);
" your_database_name
```

### 测试用例2：无效通话ID
```bash
#!/bin/bash

API_URL="http://your-domain/api/video/settle_call"
TOKEN="your_user_token_here"

echo "=== 测试用例2：无效通话ID ==="
curl -X POST "$API_URL" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $TOKEN" \
-d '{
    "call_id": 99999
}' | jq '.'
```

### 测试用例3：缺少参数
```bash
#!/bin/bash

API_URL="http://your-domain/api/video/settle_call"
TOKEN="your_user_token_here"

echo "=== 测试用例3：缺少call_id参数 ==="
curl -X POST "$API_URL" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $TOKEN" \
-d '{}' | jq '.'
```

### 测试用例4：无权限结算
```bash
#!/bin/bash

API_URL="http://your-domain/api/video/settle_call"
OTHER_USER_TOKEN="other_user_token_here"  # 非通话参与者的token
CALL_ID=1

echo "=== 测试用例4：无权限结算 ==="
curl -X POST "$API_URL" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $OTHER_USER_TOKEN" \
-d "{
    \"call_id\": $CALL_ID
}" | jq '.'
```

### 测试用例5：重复结算
```bash
#!/bin/bash

API_URL="http://your-domain/api/video/settle_call"
TOKEN="your_user_token_here"
CALL_ID=1

echo "=== 测试用例5：重复结算已结束的通话 ==="
curl -X POST "$API_URL" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $TOKEN" \
-d "{
    \"call_id\": $CALL_ID
}" | jq '.'
```

## 🔍 完整测试脚本

### test_settle_call.sh
```bash
#!/bin/bash

# 配置信息
API_BASE_URL="http://your-domain/api"
DB_USER="root"
DB_PASS="your_password"
DB_NAME="your_database"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_test() {
    echo -e "${YELLOW}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# 获取用户token
get_token() {
    local mobile=$1
    local password=$2
    
    response=$(curl -s -X POST "$API_BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"mobile\":\"$mobile\",\"password\":\"$password\"}")
    
    echo $response | jq -r '.data.token // empty'
}

# 创建测试通话记录
create_test_call() {
    mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
    INSERT INTO la_video_call_record (
        user_id, call_be_user_id, channel_id, status, unit_price, 
        turn_time, create_time, update_time
    ) VALUES (
        1001, 1002, 'test_channel_$(date +%s)', 1, 10.00,
        UNIX_TIMESTAMP() - 180, UNIX_TIMESTAMP() - 200, UNIX_TIMESTAMP()
    );
    SELECT LAST_INSERT_ID() as call_id;
    " | tail -n 1
}

# 主测试流程
main() {
    print_test "开始结算通话API测试"
    
    # 获取token
    print_test "获取用户token"
    TOKEN=$(get_token "13800001001" "123456")
    if [ -z "$TOKEN" ]; then
        print_error "获取token失败"
        exit 1
    fi
    print_success "Token获取成功"
    
    # 创建测试通话记录
    print_test "创建测试通话记录"
    CALL_ID=$(create_test_call)
    print_success "测试通话记录创建成功，ID: $CALL_ID"
    
    # 测试正常结算
    print_test "测试正常结算"
    response=$(curl -s -X POST "$API_BASE_URL/video/settle_call" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"call_id\":$CALL_ID}")
    
    code=$(echo $response | jq -r '.code')
    if [ "$code" = "1" ]; then
        print_success "结算成功"
        echo $response | jq '.'
    else
        print_error "结算失败"
        echo $response | jq '.'
    fi
    
    # 验证数据库结果
    print_test "验证数据库结果"
    mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
    SELECT 
        id, status, duration, actual_coin, actual_income, 
        actual_commission_rate, hangup_user_id, end_time
    FROM la_video_call_record 
    WHERE id = $CALL_ID;
    "
    
    print_test "测试完成"
}

# 执行测试
main
```

## 🚀 使用方法

1. **修改配置**
   ```bash
   # 编辑脚本中的配置信息
   API_BASE_URL="http://your-domain/api"
   DB_USER="root"
   DB_PASS="your_password"
   DB_NAME="your_database"
   ```

2. **赋予执行权限**
   ```bash
   chmod +x test_settle_call.sh
   ```

3. **运行测试**
   ```bash
   ./test_settle_call.sh
   ```

## 📊 预期结果

### 成功结算后的数据库状态
- `status`: 2 (已结束)
- `duration`: 180 (3分钟的秒数)
- `actual_coin`: 30.00 (3分钟 × 10金币)
- `actual_income`: 21.00 (假设抽成30%)
- `actual_commission_rate`: 30.00
- `hangup_user_id`: 1001 (发起用户)
- `end_time`: 当前时间戳

### 用户状态恢复
- 用户1001和1002的`is_busy`字段都应该变为0
