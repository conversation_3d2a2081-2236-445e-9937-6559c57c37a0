<?php

namespace app\adminapi\lists\finance;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\pay\PayConfig;
use app\common\lists\ListsSearchInterface;


/**
 * PayConfig列表
 * Class PayConfigLists
 * @package app\adminapi\lists
 */
class PayConfigLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function setSearch(): array
    {
        return [];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function lists(): array
    {
        $list = PayConfig::field(['id', 'name', 'pay_way', 'config', 'icon', 'sort', 'status'])
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['id' => 'desc'])
            ->select()
            ->toArray();
        foreach ($list as &$item) {
            $item['config'] = json_decode($item['config'], true);
        }
        return $list;
    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function count(): int
    {
        return PayConfig::where($this->searchWhere)->count();
    }

}