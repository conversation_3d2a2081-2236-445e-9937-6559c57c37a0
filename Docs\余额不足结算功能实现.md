# 余额不足结算功能实现

## 📋 功能概述

当用户在视频通话过程中余额不足时，系统会自动结束通话并根据实际通话时长计算应扣除的金额、实际收益和抽成比例。

## 🔧 实现内容

### 1. 数据库字段扩展

为 `la_video_call_record` 表添加了三个新字段：

- `actual_coin` - 实际花费金币数量（挂断时计算）
- `actual_income` - 实际获得收益（挂断时计算）  
- `actual_commission_rate` - 实际抽成比例（挂断时计算）

### 2. 业务逻辑优化

修改了 `VideoCallLogic::endCallForInsufficientBalance()` 方法：

#### 原有逻辑
- 仅更新通话状态为结束
- 记录挂断用户和结束时间
- 恢复双方用户状态

#### 新增逻辑
- **计算实际通话时长**：基于 `turn_time`（接通时间）到当前时间
- **计算实际应扣金币**：按分钟计费，不足1分钟按1分钟算
- **计算实际抽成比例**：获取接听用户的抽成设置
- **计算实际收益**：基于实际金币和抽成比例计算
- **记录实际数据**：将计算结果保存到新增字段中

## 💡 核心算法

```php
// 1. 计算实际通话时长（秒）
$actualDuration = $callRecord['turn_time'] > 0 ? 
    $currentTime - $callRecord['turn_time'] : 0;

// 2. 计算实际应扣金币（按分钟，不足1分钟按1分钟）
$actualMinutes = $actualDuration > 0 ? ceil($actualDuration / 60) : 0;
$actualCoin = $actualMinutes * $callRecord['unit_price'];

// 3. 获取实际抽成比例
$actualCommissionRate = self::calculateVideoCallCommissionRate($receiverUser);

// 4. 计算实际收益
$actualIncome = round($actualCoin * (100 - $actualCommissionRate) / 100, 2);
```

## 📊 使用场景

### 场景1：通话5分钟后余额不足
- **单价**：10金币/分钟
- **实际时长**：5分钟
- **实际扣费**：50金币
- **抽成比例**：30%
- **实际收益**：35金币

### 场景2：通话30秒后余额不足  
- **单价**：10金币/分钟
- **实际时长**：30秒
- **实际扣费**：10金币（不足1分钟按1分钟）
- **抽成比例**：30%
- **实际收益**：7金币

## 🔍 数据查询示例

```sql
-- 查看余额不足结束的通话记录
SELECT 
    id,
    user_id,
    call_be_user_id,
    duration,
    unit_price,
    total_coin,
    actual_coin,
    actual_income,
    actual_commission_rate,
    hangup_user_id,
    create_time,
    end_time
FROM la_video_call_record 
WHERE status = 2 
AND hangup_user_id = user_id  -- 付费方挂断
ORDER BY create_time DESC;
```

## 🚀 部署步骤

1. **执行数据库脚本**
   ```bash
   mysql -u username -p database_name < Docs/add_video_call_actual_fields.sql
   ```

2. **代码已自动部署**
   - 修改的文件：`app/applent/logic/video/VideoCallLogic.php`
   - 方法：`endCallForInsufficientBalance()`

3. **测试验证**
   - 发起视频通话
   - 等待余额不足自动挂断
   - 检查数据库记录中的新字段值

## ⚠️ 注意事项

- 新字段仅在余额不足自动挂断时才会被计算和填充
- 正常挂断的通话记录这些字段保持默认值0
- 实际扣费按分钟计算，与实时扣费逻辑保持一致
- 抽成比例使用接听用户的个人设置或系统默认值

## 🚀 新增API接口

### 结算通话API
**接口地址：** `/api/video/settle_call`
**请求方式：** POST
**功能说明：** 手动触发通话结算，调用余额不足结算逻辑

**请求参数：**
```json
{
    "call_id": 123
}
```

**成功响应：**
```json
{
    "code": 1,
    "msg": "通话结算成功",
    "data": {
        "call_id": 123,
        "message": "通话已结算完成",
        "settle_time": 1691234567
    }
}
```

**使用场景：**
- 管理员手动结算异常通话
- 用户主动结算无响应通话
- 系统维护时批量结算

## 🔗 相关文件

### 核心文件
- **控制器**：`app/applent/controller/video/VideoController.php`
- **业务逻辑**：`app/applent/logic/video/VideoCallLogic.php`
- **验证器**：`app/applent/validate/video/VideoCallValidate.php`
- **模型文件**：`app/common/model/VideoCallRecord.php`

### 文档文件
- **数据库脚本**：`Docs/add_video_call_actual_fields.sql`
- **API文档**：`Docs/结算通话API文档.md`
- **测试用例**：`Docs/余额不足结算功能测试用例.md`
