-- 为la_video_call_record表添加实际结算相关字段
-- 执行时间：2025-08-04

-- 添加实际花费金币数量字段
ALTER TABLE `la_video_call_record` 
ADD COLUMN `actual_coin` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际花费金币数量（挂断时计算）' AFTER `total_coin`;

-- 添加实际获得收益字段
ALTER TABLE `la_video_call_record` 
ADD COLUMN `actual_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际获得收益（挂断时计算）' AFTER `actual_coin`;

-- 添加实际抽成比例字段
ALTER TABLE `la_video_call_record` 
ADD COLUMN `actual_commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '实际抽成比例（挂断时计算）' AFTER `actual_income`;

-- 查看表结构确认字段已添加
-- DESCRIBE `la_video_call_record`;

-- 示例查询：查看包含新字段的通话记录
-- SELECT id, user_id, call_be_user_id, duration, unit_price, total_coin, actual_coin, actual_income, actual_commission_rate, status, create_time, end_time 
-- FROM `la_video_call_record` 
-- WHERE status = 2 
-- ORDER BY create_time DESC 
-- LIMIT 10;
