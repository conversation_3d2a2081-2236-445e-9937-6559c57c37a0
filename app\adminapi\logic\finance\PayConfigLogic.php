<?php

namespace app\adminapi\logic\finance;


use app\common\model\pay\PayConfig;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * PayConfig逻辑
 * Class PayConfigLogic
 * @package app\adminapi\logic
 */
class PayConfigLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            $config = [
                'pid' => $params['pid'],
                'apiurl' => $params['apiurl'],
                'type' => $params['type'],
                'className' => $params['className'],
                'totalAmount' => $params['totalAmount'],
                'platform_public_key' => $params['platform_public_key'],
                'merchant_private_key' => $params['merchant_private_key']
            ];
            PayConfig::create([
                'name' => $params['name'],
                'pay_way' => $params['pay_way'],
                'config' => json_encode($config,JSON_UNESCAPED_SLASHES),
                'icon' => $params['icon'],
                'sort' => $params['sort'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            $config = [
               'pid' => $params['pid'],
               'apiurl' => $params['apiurl'],
               'type' => $params['type'],
               'className' => $params['className'],
               'totalAmount' => $params['totalAmount'],
               'platform_public_key' => $params['platform_public_key'],
               'merchant_private_key' => $params['merchant_private_key']
            ];

            PayConfig::where('id', $params['id'])->update([
                'name' => $params['name'],
                'pay_way' => $params['pay_way'],
                'config' => json_encode($config,JSON_UNESCAPED_SLASHES),
                'icon' => $params['icon'],
                'sort' => $params['sort']
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public static function delete(array $params): bool
    {
        return PayConfig::destroy($params['id']);
    }


    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public static function detail($params): array
    {
        $info = PayConfig::findOrEmpty($params['id'])->toArray();
        $config = json_decode($info['config'], true);
        $info['pid'] = $config['pid'] ?? '';
        $info['apiurl'] = $config['apiurl'] ?? '';
        $info['type'] = $config['type'] ?? '';
        $info['className'] = $config['className'] ?? '';
        $info['totalAmount'] = $config['totalAmount'] ?? '';
        $info['platform_public_key'] = $config['platform_public_key'] ?? '';
        $info['merchant_private_key'] = $config['merchant_private_key'] ?? '';
        unset($info['config']);

        return $info;
    }

    /**
     * @notes 更改状态
     * @param array $params
     * @return
     */
    public static function updateStatus(array $params)
    {
        Db::startTrans();
        try {
            $payConfig = PayConfig::find($params['id']);
            $payConfig->status = $params['status'];
            $payConfig->update_time = time();
            $payConfig->save();
            Db::commit();
            return true;
        }catch (\Exception $e){
            Db::rollback();
            return $e->getMessage();
        }

    }
}