# 通话计费规则和精度优化

## 概述

针对视频通话结算功能进行了计费规则优化和数值精度处理，确保计费的公平性和计算的准确性。

## 主要改进

### 1. 计费规则优化

#### 免费通话时长
- **规则**：通话时长小于5秒不扣费
- **目的**：避免因网络问题或误操作导致的不合理扣费
- **实现**：只有通话时长 ≥ 5秒才开始按分钟计费

```php
// 小于5秒不扣费，5秒及以上按分钟计费
$actualMinutes = 0;
if ($actualDuration >= 5) {
    $actualMinutes = ceil($actualDuration / 60);
}
$actualCoin = $actualMinutes * $callRecord['unit_price'];
```

#### 按分钟计费规则
- **规则**：不足1分钟按1分钟计算
- **实现**：使用 `ceil()` 函数向上取整
- **示例**：
  - 5秒 → 1分钟 → 扣费
  - 65秒 → 2分钟 → 扣费
  - 120秒 → 2分钟 → 扣费

### 2. 数值精度优化

#### 问题分析
浮点数运算可能存在精度问题，例如：
```php
// 可能出现精度问题的代码
$balanceDifference = $actualCoin - $totalPaidCoin;
if ($balanceDifference > 0) {
    // 可能因为精度问题导致判断错误
}
```

#### 解决方案
**金币计算**：由于金币都是整型，无需使用BC Math函数
```php
// 金币差价计算（整型，无精度问题）
$balanceDifference = $actualCoin - $totalPaidCoin;

if ($balanceDifference > 0) {
    // 实际费用 > 已扣费用，需要补扣差价
    UserBalance::subBalance($callRecord['user_id'], $balanceDifference);
} elseif ($balanceDifference < 0) {
    // 实际费用 < 已扣费用，多扣了需要退还
    UserBalance::addBalance($callRecord['user_id'], abs($balanceDifference));
}
```

**收益计算**：涉及小数运算，使用BC Math函数确保精度
```php
// 收益计算使用精确计算
$afterIncome = bcadd($beforeIncome, $actualIncome, 2);
```

#### BC Math 函数说明
- `bccomp($a, $b, $scale)`: 比较两个数值，返回 -1、0、1
- `bcsub($a, $b, $scale)`: 精确减法运算
- `bcadd($a, $b, $scale)`: 精确加法运算
- `$scale`: 小数点后保留位数，金额计算使用2位

## 计费场景示例

### 场景1：正常通话
```
通话时长: 125秒
单价: 10金币/分钟
计算: ceil(125/60) = 3分钟
实际扣费: 3 × 10 = 30金币
```

### 场景2：短时通话（不扣费）
```
通话时长: 3秒
单价: 10金币/分钟
计算: 3 < 5秒，不扣费
实际扣费: 0金币
```

### 场景3：刚好5秒
```
通话时长: 5秒
单价: 10金币/分钟
计算: ceil(5/60) = 1分钟
实际扣费: 1 × 10 = 10金币
```

### 场景4：差价处理
```
实际应扣: 30.00金币
已扣费用: 25.50金币
差价: 30.00 - 25.50 = 4.50金币
处理: 补扣4.50金币
```

## 精度处理策略

### 金币计算（整型，无精度问题）
```php
// 金币都是整数，直接计算即可
$actualCoin = 30;        // 整型金币
$totalPaidCoin = 25;     // 整型金币
$difference = $actualCoin - $totalPaidCoin; // 结果：5，无精度问题

if ($difference > 0) {
    // 直接判断，无精度问题
    UserBalance::subBalance($userId, $difference);
}
```

### 收益计算（小数，需要精确计算）
```php
// 收益涉及小数，使用BC Math确保精度
$actualIncome = 15.75;   // 小数收益
$beforeIncome = 100.25;  // 小数收益
$afterIncome = bcadd($beforeIncome, $actualIncome, 2); // 精确计算：116.00
```

## 业务影响

### 用户体验改善
1. **避免误扣费**：5秒内的短时通话不收费
2. **计费透明**：明确的按分钟计费规则
3. **精确计算**：避免因精度问题导致的计费错误

### 系统稳定性提升
1. **数据准确性**：精确的数值计算
2. **一致性保证**：避免浮点数精度导致的数据不一致
3. **审计友好**：精确的金额记录便于对账

## 配置建议

### PHP 配置
确保启用 BC Math 扩展：
```ini
extension=bcmath
```

### 数据库字段
金额相关字段建议使用 DECIMAL 类型：
```sql
`actual_coin` DECIMAL(10,2) DEFAULT 0.00,
`actual_income` DECIMAL(10,2) DEFAULT 0.00,
`total_coin` DECIMAL(10,2) DEFAULT 0.00
```

## 测试用例

### 计费规则测试
```php
// 测试5秒以下不扣费
testCallDuration(3, 10, 0);  // 3秒，期望0金币
testCallDuration(5, 10, 10); // 5秒，期望10金币
testCallDuration(65, 10, 20); // 65秒，期望20金币

function testCallDuration($duration, $unitPrice, $expected) {
    $actualMinutes = 0;
    if ($duration >= 5) {
        $actualMinutes = ceil($duration / 60);
    }
    $actualCoin = $actualMinutes * $unitPrice;
    
    assert($actualCoin == $expected, "计费测试失败");
}
```

### 精度测试
```php
// 测试精度计算
function testPrecision() {
    $a = '30.00';
    $b = '29.99';
    
    // 使用浮点数计算（可能有问题）
    $floatDiff = (float)$a - (float)$b;
    
    // 使用BC Math计算（精确）
    $bcDiff = bcsub($a, $b, 2);
    
    echo "浮点数差值: " . $floatDiff . "\n";
    echo "BC Math差值: " . $bcDiff . "\n";
}
```

## 监控指标

### 业务指标
- 5秒内通话占比
- 差价补扣/退还频率
- 平均通话时长
- 计费准确率

### 技术指标
- BC Math 函数执行时间
- 精度计算错误率
- 数据库DECIMAL字段性能

## 注意事项

1. **性能考虑**：BC Math 函数比普通运算稍慢，但精度更高
2. **数据类型**：确保传入BC Math函数的参数为字符串类型
3. **小数位数**：统一使用2位小数进行金额计算
4. **向下兼容**：确保现有数据能正确处理

## 后续优化建议

1. **配置化**：将5秒免费时长设为可配置参数
2. **分级计费**：考虑不同用户等级的计费规则
3. **优惠策略**：支持首次通话免费等营销策略
4. **实时监控**：添加计费异常的实时监控和告警
