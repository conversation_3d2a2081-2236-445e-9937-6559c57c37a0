<?php

namespace app\applent\lists\wallet;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\user\UserCoinLog;
use app\common\service\ConfigService;
/**
 * 钱包明细支出列表
 * Class WalletDetailLists
 * @package app\applent\lists\wallet
 */
class ExpenditureLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $searchFields = ['date'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params),$searchFields);
    }

    /**
     * @notes 设置列表字段
     * @return array
     */
    public function setFields(): array
    {
        return [
            'id',
            'source_no',
            'to_user_id',
            'to_nickname',
            'type',
            'change_coin',
            'source',
            'num',
            'price',
            'name',
            'create_time',
        ];
    }

    /**
     * @notes 设置排序
     * @return array
     */
    public function setOrder(): array
    {
        return ['create_time' => 'desc'];
    }

    /**
     * @notes 获取列表数据
     * @return array
     */
    public function lists(): array
    {
        $lists = UserCoinLog::field($this->setFields())
            ->where(['user_id' => $this->userId,'type' => 2])
            ->withSearch($this->setSearch(), $this->params)
            ->order($this->setOrder())
            ->page($this->pageNo, $this->pageSize)
            ->select()
            ->toArray();

        return $this->createReturn($lists);
    }

    /**
     * @notes 处理列表数据
     * @param array $lists
     * @return array
     */
    public function createReturn(array $lists): array
    {
        $data = [];
        $currencyName = ConfigService::get('systemconfig', 'currency_name', '金币');

        foreach ($lists as $item) {
            // 根据source字段处理不同的显示逻辑
            $processedItem = $this->processItemBySource($item, $currencyName);
            $data[] = $processedItem;
        }

        return $data;
    }

    /**
     * @notes 根据source处理不同的数据显示
     * @param array $item
     * @param string $currencyName
     * @return array
     */
    private function processItemBySource($item, $currencyName)
    {
        $result = [
            'id' => $item['id'],
            'source_no' => $item['source_no'],
            'to_user_id' => $item['to_user_id'],
            'to_nickname' => $item['to_nickname'],
            'type' => $item['type'],
            'change_coin' => $item['change_coin'],
            'source' => $item['source'],
            'create_time' => $item['create_time'],
            'currency_name' => $currencyName,
        ];

        // 根据source字段设置不同的显示内容
        switch ($item['source']) {
            case 2: // 礼物赠送
                $result['title'] = '送出礼物';
                $result['quantity'] = $item['num'];
                $result['unit_price'] = $item['price'].$currencyName;
                $result['item_name'] = $item['name'] ?: '礼物';
                break;

            case 3: // 音频通话
                $result['title'] = '音频通话';
                $result['quantity'] = $item['num'];
                $result['unit_price'] = $item['price'].$currencyName;
                $result['item_name'] = '分钟';
                break;

            default:
                $result['title'] = '其他消费';
                $result['quantity'] = 0;
                $result['unit_price'] = 0;
                $result['item_name'] = '';
                break;
        }

        return $result;
    }

    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return UserCoinLog::where(['user_id' => $this->userId, 'type' => 2])
            ->withSearch($this->setSearch(), $this->params)
            ->count();
    }
}
