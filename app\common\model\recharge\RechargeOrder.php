<?php

namespace app\common\model\recharge;

use app\common\enum\PayEnum;
use app\common\model\BaseModel;
use think\model\concern\SoftDelete;

/**
 * 充值订单模型
 * Class RechargeOrder
 * @package app\common\model
 */
class RechargeOrder extends BaseModel
{
    /**
     * @notes 按月份搜索器
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchDateAttr($query, $value, $data)
    {
        if (!empty($value)) {
            // 解析 YYYY-MM 格式
            $parts = explode('-', $value);
            if (count($parts) == 2) {
                $year = (int)$parts[0];
                $month = (int)$parts[1];

                // 计算该月的开始和结束时间
                $startTime = strtotime($year . '-' . sprintf('%02d', $month) . '-01 00:00:00');
                $endTime = strtotime(date('Y-m-t 23:59:59', $startTime)); // 该月最后一天

                $query->where('create_time', 'between', [$startTime, $endTime]);
            }
        } else {
            // 如果没有传入日期，默认查找最近一周的数据
            $startTime = strtotime('-7 days');
            $endTime = time();
            $query->where('create_time', 'between', [$startTime, $endTime]);
        }
    }

    /**
     * @notes 搜索器-支付方式
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchPayTypeAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('pay_type', '=', $value);
        }
    }

    /**
     * @notes 支付方式文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getPayTypeTextAttr($value, $data)
    {
        return PayEnum::getPayTypeDesc($data['pay_type']);
    }

    /**
     * @notes 支付状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getPayStatusTextAttr($value, $data)
    {
        return PayEnum::getPayStatusDesc($data['pay_status']);
    }

    /**
     * @notes 支付状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsFirstOrderTextAttr($value, $data)
    {
        return PayEnum::getIsFirstOrderDesc($data['is_first_order']);
    }
}