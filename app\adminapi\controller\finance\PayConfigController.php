<?php


namespace app\adminapi\controller\finance;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\finance\PayConfigLists;
use app\adminapi\logic\finance\PayConfigLogic;
use app\adminapi\validate\finance\PayConfigValidate;


/**
 * PayConfig控制器
 * Class PayConfigController
 * @package app\adminapi\controller
 */
class PayConfigController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function lists()
    {
        return $this->dataLists(new PayConfigLists());
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function add()
    {
        $params = (new PayConfigValidate())->post()->goCheck('add');
        $result = PayConfigLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(PayConfigLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function edit()
    {
        $params = (new PayConfigValidate())->post()->goCheck('edit');
        $result = PayConfigLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(PayConfigLogic::getError());
    }


    /**
     * @notes 删除
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function delete()
    {
        $params = (new PayConfigValidate())->post()->goCheck('delete');
        PayConfigLogic::delete($params);
        return $this->success('删除成功', [], 1, 1);
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function detail()
    {
        $params = (new PayConfigValidate())->goCheck('detail');
        $result = PayConfigLogic::detail($params);
        return $this->data($result);
    }


    /**
     * @notes  更改状态
     * @return \think\response\Json
     */
    public function updateStatus()
    {
        $params = (new PayConfigValidate())->post()->goCheck('status');
        $res = PayConfigLogic::updateStatus($params);
        if (true === $res) {
            return $this->success('修改成功', [], 1, 1);
        }
        return $this->fail($res);
    }


}