# 余额不足结算功能测试用例

## 🧪 测试环境准备

### 1. 数据库准备
```sql
-- 确保la_video_call_record表有新字段
DESCRIBE la_video_call_record;

-- 创建测试用户
INSERT INTO la_user (id, nickname, mobile, avatar, sex) VALUES 
(1001, '测试用户A', '13800001001', 'default_avatar.jpg', 1),
(1002, '测试用户B', '13800001002', 'default_avatar.jpg', 2);

-- 设置用户余额
INSERT INTO la_user_balance (user_id, balance, income) VALUES 
(1001, 25, 0),  -- 用户A有25金币
(1002, 0, 0);   -- 用户B无余额

-- 设置用户抽成比例（可选）
INSERT INTO la_user_rebate_ratio (user_id, audio_commission) VALUES 
(1002, 30.00);  -- 用户B抽成30%
```

### 2. 系统配置
```php
// 确保系统配置
'video_deduction' => 10,  // 视频通话每分钟10金币
'audio_commission' => 50, // 默认抽成50%
```

## 📋 测试用例

### 测试用例1：通话3分钟后余额不足
**测试步骤：**
1. 用户A（25金币）向用户B发起视频通话
2. 用户B接听通话
3. 通话进行3分钟（180秒）
4. 系统检测余额不足，自动挂断

**预期结果：**
```sql
SELECT 
    duration,           -- 应为180秒左右
    actual_coin,        -- 应为30金币（3分钟 × 10金币）
    actual_income,      -- 应为21金币（30 × 70%）
    actual_commission_rate, -- 应为30.00
    hangup_user_id,     -- 应为1001（付费方）
    status              -- 应为2（已结束）
FROM la_video_call_record 
WHERE user_id = 1001 AND call_be_user_id = 1002;
```

### 测试用例2：通话30秒后余额不足
**测试步骤：**
1. 用户A（5金币）向用户B发起视频通话
2. 用户B接听通话
3. 通话进行30秒
4. 系统检测余额不足，自动挂断

**预期结果：**
```sql
SELECT 
    duration,           -- 应为30秒左右
    actual_coin,        -- 应为10金币（不足1分钟按1分钟）
    actual_income,      -- 应为7金币（10 × 70%）
    actual_commission_rate, -- 应为30.00
    hangup_user_id,     -- 应为1001（付费方）
    status              -- 应为2（已结束）
FROM la_video_call_record 
WHERE user_id = 1001 AND call_be_user_id = 1002;
```

### 测试用例3：未接通就余额不足
**测试步骤：**
1. 用户A（5金币）向用户B发起视频通话
2. 用户B未接听
3. 系统检测余额不足，自动挂断

**预期结果：**
```sql
SELECT 
    duration,           -- 应为0
    actual_coin,        -- 应为0（未接通）
    actual_income,      -- 应为0
    actual_commission_rate, -- 应为30.00
    hangup_user_id,     -- 应为1001（付费方）
    status              -- 应为2（已结束）
FROM la_video_call_record 
WHERE user_id = 1001 AND call_be_user_id = 1002;
```

## 🔍 API测试

### 1. 实时扣费API测试
```bash
# 模拟实时扣费请求
curl -X POST "http://your-domain/api/video/real_time_charge" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer USER_TOKEN" \
-d '{
    "call_id": 123,
    "user_id": 1001
}'
```

**余额不足时的响应：**
```json
{
    "code": 0,
    "msg": "余额不足以支付后续通话费用，通话已结束",
    "data": null
}
```

### 2. 验证数据库记录
```sql
-- 检查通话记录是否正确更新
SELECT 
    id,
    user_id,
    call_be_user_id,
    status,
    duration,
    unit_price,
    total_coin,
    actual_coin,
    actual_income,
    actual_commission_rate,
    hangup_user_id,
    create_time,
    end_time
FROM la_video_call_record 
WHERE status = 2 
AND hangup_user_id = user_id
ORDER BY create_time DESC 
LIMIT 5;
```

## ✅ 验证检查点

### 1. 数据完整性检查
- [ ] `actual_coin` 字段值正确（基于实际时长计算）
- [ ] `actual_income` 字段值正确（基于抽成比例计算）
- [ ] `actual_commission_rate` 字段值正确（获取用户设置）
- [ ] `duration` 字段更新为实际通话时长
- [ ] `hangup_user_id` 设置为付费方用户ID

### 2. 业务逻辑检查
- [ ] 余额不足时通话自动结束
- [ ] 双方用户状态恢复为非忙碌
- [ ] 不足1分钟按1分钟计费
- [ ] 抽成比例使用用户个人设置或系统默认值

### 3. 边界情况检查
- [ ] 通话时长为0时的处理
- [ ] 用户无抽成设置时使用默认值
- [ ] 接听用户不存在时的异常处理

## 🐛 常见问题排查

### 问题1：字段不存在错误
```
Column 'actual_coin' doesn't exist
```
**解决方案：** 执行数据库脚本添加字段

### 问题2：抽成比例计算错误
**检查：** 用户是否有个人抽成设置，系统默认配置是否正确

### 问题3：时长计算异常
**检查：** `turn_time` 字段是否正确记录接通时间

## 📊 性能监控

监控以下指标：
- 余额不足自动挂断的频率
- 实际结算金额与预期的差异
- 数据库更新操作的执行时间
- 用户状态恢复的成功率
