<?php

namespace app\adminapi\validate\finance;


use app\common\validate\BaseValidate;


/**
 * PayConfig验证器
 * Class PayConfigValidate
 * @package app\adminapi\validate
 */
class PayConfigValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require'

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id'

    ];


    /**
     * @notes 添加场景
     * @return PayConfigValidate
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function sceneAdd()
    {
        return $this->only(['name','pay_way','status']);
    }


    /**
     * @notes 编辑场景
     * @return PayConfigValidate
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function sceneEdit()
    {
        return $this->only(['id','name','pay_way','status']);
    }


    /**
     * @notes 删除场景
     * @return PayConfigValidate
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return PayConfigValidate
     * <AUTHOR>
     * @date 2025/08/04 12:49
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}