<?php

namespace app\adminapi\lists\finance;

use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsExcelInterface;
use app\common\lists\ListsSearchInterface;
use app\common\model\recharge\RechargeOrder;
use app\common\model\user\User;
use app\common\model\user\UserInviteRecord;
use app\common\service\FileService;

/**
 * 充值记录列表
 * Class RecharLists
 * @package app\adminapi\lists
 */
class RechargeLists extends BaseAdminDataLists implements ListsSearchInterface, ListsExcelInterface
{
    /**
     * @notes 导出字段
     * @return string[]
     */
    public function setExcelFields(): array
    {
        return [
            'id'=>'ID',
            'user_info'=>'用户昵称（ID）',
            'mobile' => '手机号码',
            'invite_info'=>'邀请人（ID）',
            'order_no' => '充值单号',
            'pay_status_text' => '支付状态',
            'pay_amount' => '充值金额',
            'coin_amount' => '充值金币数',
            'bonus_coin' => '赠送金币数',
            'total_coin' => '总金币数',
            'pay_type_text' => '支付方式',
            'serial_number' => '支付流水号',
            'is_first_order_text' => '是否首充',
            'create_time' => '充值时间',
            'pay_time' => '支付时间',
        ];
    }


    /**
     * @notes 导出表名
     * @return string
     */
    public function setFileName(): string
    {
        return '充值记录';
    }


    /**
     * @notes 搜索条件
     * @return \string[][]
     */
    public function setSearch(): array
    {
        return [
            '=' => ['ro.order_no', 'ro.pay_type', 'ro.pay_status'],
        ];
    }


    /**
     * @notes 搜索条件
     */
    public function queryWhere()
    {
        $where = [];
        // 用户编号
        if (!empty($this->params['user_info'])) {
            $where[] = ['u.id|u.nickname|u.mobile', 'like', '%' . $this->params['user_info'] . '%'];
        }

        // 下单时间
        if (!empty($this->params['start_time']) && !empty($this->params['end_time'])) {
            $time = [strtotime($this->params['start_time']), strtotime($this->params['end_time'])];
            $where[] = ['ro.create_time', 'between', $time];
        }

        return $where;
    }


    /**
     * @notes 获取列表
     * @return array
     */
    public function lists(): array
    {
        $field = 'ro.id,ro.user_id,ro.order_no,ro.pay_status,ro.pay_amount,ro.coin_amount,ro.bonus_coin,ro.total_coin,ro.pay_type,ro.serial_number,ro.is_first_order,ro.create_time,ro.pay_time';
        $field .= ',u.nickname,u.mobile';
        $lists = RechargeOrder::alias('ro')
            ->join('user u', 'u.id = ro.user_id')
            ->field($field)
            ->where($this->queryWhere())
            ->where($this->searchWhere)
            ->order('ro.id', 'desc')
            ->limit($this->limitOffset, $this->limitLength)
            ->append(['pay_status_text', 'pay_type_text','is_first_order_text'])
            ->select()
            ->toArray();

        foreach ($lists as &$item) {
            $item['user_info'] = $item['nickname'] . '(' . $item['user_id'] . ')';

            $item['pay_time'] = empty($item['pay_time']) ? '' : date('Y-m-d H:i:s', $item['pay_time']);
            $invite = UserInviteRecord::field('user_id,invite_user_id',)->where(['invite_user_id'=>$item['user_id']])->findOrEmpty();
            if (!empty($invite->toArray())) {
                $inviteInfo = User::field('id,nickname')->where(['id'=>$invite['user_id']])->findOrEmpty();
                $item['invite_info'] = $inviteInfo['nickname'] . '(' . $inviteInfo['id'] . ')';
            }else{
                $item['invite_info'] = '';
            }

        }

        return $lists;
    }


    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return RechargeOrder::alias('ro')
            ->join('user u', 'u.id = ro.user_id')
            ->where($this->queryWhere())
            ->where($this->searchWhere)
            ->count();
    }


}